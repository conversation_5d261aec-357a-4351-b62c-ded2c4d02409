$(document).ready(function() {
    // 获取URL参数
    const urlParams = new URLSearchParams(window.location.search);
    const routeId = urlParams.get('id');
    const routeName = urlParams.get('name');
    
    // 初始化页面
    init();
    
    function init() {
        // 显示线路名称
        if (routeName) {
            $('#route-name').text(decodeURIComponent(routeName));
        }
        
        // 开始更新时间
        updateTime();
        setInterval(updateTime, 1000);
        
        // 开始倒计时
        startCountdown();
        
        // 加载线路数据
        loadRouteData(routeId);
        
        // 定时刷新数据
        setInterval(function() {
            loadRouteData(routeId);
        }, 30000); // 30秒刷新一次
    }
    
    function updateTime() {
        const now = new Date();
        const timeStr = formatTime(now);
        const dateStr = formatDate(now);
        $('#current-time').text(`${dateStr} ${timeStr}`);
    }
    
    // 全局倒计时定时器
    let countdownTimer = null;

    function startCountdown() {
        // 初始显示，等待从API获取真实数据
        $('#countdown-timer').text('加载中...');
    }

    function updateCountdownTimer(timeStr) {
        // 清除旧的定时器
        if (countdownTimer) {
            clearInterval(countdownTimer);
            countdownTimer = null;
        }

        let totalSeconds = parseTimeToSeconds(timeStr);

        // 立即更新一次显示
        updateCountdownDisplay(totalSeconds);

        // 开始新的倒计时
        countdownTimer = setInterval(function() {
            totalSeconds--;
            updateCountdownDisplay(totalSeconds);

            if (totalSeconds < 0) {
                clearInterval(countdownTimer);
                countdownTimer = null;
                $('#countdown-timer').text('00:00:00');
                $('#countdown-timer').css('color', '#FF4444');
            }
        }, 1000);
    }

    function updateCountdownDisplay(totalSeconds) {
        if (totalSeconds < 0) {
            $('#countdown-timer').text('00:00:00');
            $('#countdown-timer').css('color', '#FF4444');
            return;
        }

        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        const seconds = totalSeconds % 60;

        const timeStr = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        $('#countdown-timer').text(timeStr);

        // 根据剩余时间设置颜色
        updateCountdownColor(totalSeconds);
    }

    function updateCountdownColor(totalSeconds) {
        const $timer = $('#countdown-timer');

        if (totalSeconds < 30 * 60) { // 30分钟内 - 红色
            $timer.css('color', '#FF4444');
        } else if (totalSeconds < 60 * 60) { // 1小时内 - 黄色
            $timer.css('color', '#FFB800');
        } else { // 1小时以上（包含1小时） - 绿色
            $timer.css('color', '#28A745');
        }
    }
    
    function loadRouteData(routeId) {
        $.ajax({
            url: 'api.php',
            type: 'GET',
            dataType: 'json',
            data: {
                do: 'getRouteData',
                routeId: routeId
            },
            success: function(response) {
                console.log('线路数据API响应:', response);
                if (response.success) {
                    updateRouteDisplay(response.data);
                } else {
                    console.error('加载线路数据失败:', response.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('请求失败:', error, xhr.responseText);
            }
        });
    }
    
    function updateRouteDisplay(data) {
        // 更新基本信息
        $('#route-name').text(data.routeName || '未知线路');
        $('#delivery-driver').text(data.deliveryDriver || '未分配');
        $('#sorted-count').text(data.sortedCount || 0);
        $('#unsorted-count').text(data.unsortedCount || 0);

        // 更新配送倒计时
        if (data.deliveryTime) {
            updateCountdownTimer(data.deliveryTime);
        }

        // 计算并更新进度
        const sortedCount = parseInt(data.sortedCount) || 0;
        const unsortedCount = parseInt(data.unsortedCount) || 0;
        const totalCount = sortedCount + unsortedCount;

        let progressPercent = 0;
        if (totalCount > 0) {
            progressPercent = (sortedCount / totalCount) * 100;
        }

        updateProgress(progressPercent);

        // 更新门店滚动列表
        updateStoreScroll(data.stores);

        // 更新二维码
        updateQRCode(data.qrCodeData);
    }
    
    function updateProgress(percent) {
        const formattedPercent = percent.toFixed(2);
        $('#progress-fill').css('width', percent + '%');
        $('#progress-text').text(formattedPercent + '%');
    }
    
    function updateStoreScroll(stores) {
        const $scrollContainer = $('#route-stores-scroll');
        $scrollContainer.empty();
        
        if (stores && stores.length > 0) {
            // 创建滚动内容
            stores.forEach(function(store) {
                const $storeItem = $('<div class="route-store-item"></div>');
                $storeItem.text(store.name || '未知门店');
                $scrollContainer.append($storeItem);
            });
            // 如果门店数量少，重复添加以确保滚动效果
            if (stores.length < 5) {
                stores.forEach(function(store) {
                    const $storeItem = $('<div class="route-store-item"></div>');
                    $storeItem.text(store.name || '未知门店');
                    $scrollContainer.append($storeItem);
                });
            }
            // 新增：逐条滚动逻辑
            setupStoreAutoScroll();
        } else {
            // 显示默认内容
            const $defaultItem = $('<div class="route-store-item"></div>');
            $defaultItem.text('暂无门店信息');
            $scrollContainer.append($defaultItem);
        }
    }
    
    // 逐条滚动门店
    let storeScrollTimer = null;
    function setupStoreAutoScroll() {
        const $scrollContainer = $('#route-stores-scroll');
        const $items = $scrollContainer.children('.route-store-item');
        if ($items.length <= 1) return; // 只有1个不滚动
        // 清除旧定时器
        if (storeScrollTimer) {
            clearInterval(storeScrollTimer);
            storeScrollTimer = null;
        }
        // 克隆第一个到末尾，实现无缝
        $scrollContainer.find('.clone-item').remove();
        $scrollContainer.append($items.first().clone().addClass('clone-item'));
        const itemHeight = $items.first().outerHeight(true);
        let index = 0;
        function scrollNext() {
            index++;
            $scrollContainer.css('transform', `translateY(-${index * itemHeight}px)`);
            // 到最后一个（克隆），动画结束后瞬间回到0
            if (index === $items.length) {
                setTimeout(() => {
                    $scrollContainer.css('transition', 'none');
                    $scrollContainer.css('transform', 'translateY(0)');
                    index = 0;
                    // 触发重绘
                    $scrollContainer[0].offsetHeight;
                    $scrollContainer.css('transition', '');
                }, 500);
            }
        }
        // 初始样式
        $scrollContainer.css('transition', '');
        $scrollContainer.css('transform', 'translateY(0)');
        storeScrollTimer = setInterval(scrollNext, 2000);
    }
    
    function updateQRCode(qrData) {
        // 清空二维码容器
        $('#qr-code').empty();
        
        if (qrData && qrData.lcid) {
            // 生成二维码
            const qrString = JSON.stringify(qrData);
            // 根据屏幕分辨率调整二维码尺寸
            const qrSize = 500;
            const qrcode = new QRCode(document.getElementById('qr-code'), {
                text: qrString,
                width: qrSize,
                height: qrSize,
                colorDark: '#000000',
                colorLight: '#ffffff',
                correctLevel: QRCode.CorrectLevel.M
            });
        } else {
            // 显示默认二维码
            const defaultQR = JSON.stringify({lcid: "demo"});
            const qrSize = 500;
            const qrcode = new QRCode(document.getElementById('qr-code'), {
                text: defaultQR,
                width: qrSize,
                height: qrSize,
                colorDark: '#000000',
                colorLight: '#ffffff',
                correctLevel: QRCode.CorrectLevel.M
                });
            }
        }

    // 解析时间字符串为秒数
    function parseTimeToSeconds(timeStr) {
        const parts = timeStr.split(':');
        if (parts.length !== 3) return 0;

        const hours = parseInt(parts[0]) || 0;
        const minutes = parseInt(parts[1]) || 0;
        const seconds = parseInt(parts[2]) || 0;

        return hours * 3600 + minutes * 60 + seconds;
    }

    // 工具函数
    function formatTime(date) {
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        const seconds = date.getSeconds().toString().padStart(2, '0');
        return `${hours}:${minutes}:${seconds}`;
    }
    
    function formatDate(date) {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        return `${year}-${month}-${day}`;
    }
}); 