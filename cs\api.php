<?php
/**
 * 投屏系统API接口
 * 处理门店和线路数据的获取
 * 
 * 使用方法：
 * api.php?do=getStores - 获取门店列表
 * api.php?do=getRoutes - 获取线路列表  
 * api.php?do=getStoreData&storeId=xxx - 获取门店投屏数据
 * api.php?do=getRouteData&routeId=xxx - 获取线路投屏数据
 * 
 * 注意：SQL查询中的表名和字段名需要根据实际数据库结构调整：
 * - lgt_store: 门店表（主键：lsid，状态：lstatus，类型：stype）
 * - delivery_route: 配送线路表  
 * - store_route_mapping: 门店线路关联表
 * - sorting_record: 分拣记录表（需要创建或调整为实际表名）
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// 引入系统配置
require_once('../logistics/inc/php/global.php');
control();

/**
 * 获取系统中有效的门店列表
 */
function getStoresAction() {
    $sql = "SELECT lsid as id, lsname as name FROM lgt_store WHERE lstatus = 1 AND stype <> 3 ORDER BY lsname";
    $stores = AceDB::getAll($sql);
    
    jsonResponse(true, '获取门店列表成功', $stores);
}

/**
 * 获取系统中有效的线路列表
 */
function getRoutesAction() {
    // 根据实际线路表结构调整SQL查询
    $sql = "SELECT DISTINCT lcid as id, route as name
            FROM lgt_car 
            WHERE status = 1 ORDER BY route";
    $routes = AceDB::getAll($sql);    
    jsonResponse(true, '获取线路列表成功', $routes);
}

/**
 * 获取门店投屏数据
 */
function getStoreDataAction() {
    $storeId = $_GET['storeId'] ?? '';
    if (empty($storeId)) {
        jsonResponse(false, '门店ID不能为空');
        return;
    }
    
    // 获取门店基本信息
    $sql = "SELECT lsname as name FROM lgt_store WHERE lsid = $storeId AND lstatus = 1";
    $storeInfo = AceDB::getRow($sql);
    if (!$storeInfo) {
        jsonResponse(false, '门店不存在');
        return;
    }
    
    $firstldid = AceDB::getOne("SELECT ldid FROM lgt_depot WHERE lsid = $storeId AND depotstatus = 1 LIMIT 1");
    // 获取门店分拣数据 
    $today = date('Y-m-d');
    $sql = "
    SELECT lai.is_sorted,lai.lgid,lai.lcid,las.lasid,las.slsid,las.sorttime 
    FROM lgt_applysum las 
    JOIN lgt_applysum_item lai ON las.lasid=lai.lasid
    WHERE las.arriveddate='{$today}' AND lai.bmakepur=0 AND lai.lsid= $storeId AND las.status IN (2,3)
    ";
    $list = AceDB::getAll($sql);
    $sortedDict = [];
    $allCount = count(array_unique(array_column($list, 'lgid')));

    $dict = [];
    foreach($list as $item){
        if($item['is_sorted'] == 1){
            $sortedDict[$item['lgid']] = 1;
        }
        $dict[$item['sorttime'].'_'.$item['slsid']][] = $item;
    }


    $lcid = -1;
    foreach ($dict as $key => $value) {
        $sorttime = explode('_',$key)[0];
        $slsid = explode('_',$key)[1];
        $list = $value; 
        //获取汇总单的路线
        $lasidStr = implode(',', array_unique(array_column($value, 'lasid')));
        $line = getSumLine($lasidStr, false, $sorttime, $slsid);
        $lcid = $line['shopData'][$storeId]?$line['shopData'][$storeId]['lcid']:-1;
    }
    
    
    $data = [
        'storeName' => $storeInfo['name'],// 门店名称
        'sortedCount' => count($sortedDict),// 已分拣单品数量
        'unsortedCount' => $allCount - count($sortedDict),// 未分拣单品数量
        'deliveryTime' => getDeliveryCountdown($lcid, $storeId),// 配送倒计时
        'deliveryRoute' => Car::getNameById($lcid)?:'无',
        'deliveryDriver' => getDriverName($lcid),
        'qrCodeData' => [
            'lsid' => $storeId,
            'ldid' => $firstldid
        ]
    ];
    
    jsonResponse(true, '获取门店数据成功', $data);
}

/**
 * 获取线路投屏数据
 */
function getRouteDataAction() {
    $routeId = $_GET['routeId'] ?? '';
    
    if (empty($routeId)) {
        jsonResponse(false, '线路ID不能为空');
        return;
    }


    $today = date('Y-m-d');
    $sql = "
    SELECT lai.is_sorted,lai.lgid,lai.lcid,las.lasid,las.slsid,las.sorttime,lai.lsid 
    FROM lgt_applysum las 
    JOIN lgt_applysum_item lai ON las.lasid=lai.lasid
    WHERE las.arriveddate='{$today}' AND lai.bmakepur=0 AND las.status IN (2,3)
    ";
    $list = AceDB::getAll($sql);
    $sortedDict = [];

    $dict = [];
    foreach($list as $item){
        $dict[$item['sorttime'].'_'.$item['slsid']][] = $item;
    }
    $lsidDict = [];
    $allCount = [];

    foreach ($dict as $key => $value) {
        $sorttime = explode('_',$key)[0];
        $slsid = explode('_',$key)[1];
        //获取汇总单的路线
        $lasidStr = implode(',', array_unique(array_column($value, 'lasid')));
        $line = getSumLine($lasidStr, false, $sorttime, $slsid);
        foreach($value as $item){
            if(!isset($line['shopData'][$item['lsid']]) || $line['shopData'][$item['lsid']]['lcid'] != $routeId){
                continue;
            }
            $allCount[$item['lgid']] = 1;
            if($item['is_sorted'] == 1){
                $sortedDict[$item['lgid']] = 1;
            }
            $lsidDict[$item['lsid']] = 1;
        }
    }
    if($lsidDict) {
        $sql = "
        select lsid id,lsname name 
        from lgt_store 
        where lsid in (".implode(',',array_keys($lsidDict)).") and lstatus=1 and stype<>3
        ";
    }else{
        $sql = "
        select ls.lsid id,ls.lsname name 
        from lgt_store ls 
        join lgt_car_item lci ON ls.lsid=lci.lsid
        where lci.lcid={$routeId} and ls.lstatus=1 and ls.stype<>3
        ";
    }
    $stores = AceDB::getAll($sql);
    
    // 获取线路整体分拣数据（根据实际表结构调整）
    $today = date('Y-m-d');
    
    $data = [
        'routeName' => Car::getNameById($routeId)?:'无',
        'deliveryDriver' => getDriverName($routeId)?:'无',
        'sortedCount' => count($sortedDict),
        'unsortedCount' => count($allCount) - count($sortedDict),
        'deliveryTime' => getDeliveryCountdown($routeId),// 配送倒计时
        'stores' => $stores,
        'qrCodeData' => [
            'lcid' => $routeId
        ]
    ];
    
    jsonResponse(true, '获取线路数据成功', $data);
}

function getDriverName($lcid){
    $sql = "
    select ldr.name from lgt_car_vehicle v
    join lgt_vehicle lv on v.lvid=lv.lvid
    join lgt_driver ldr on lv.ldrid=ldr.ldrid
    where v.lcid={$lcid} and ldr.status=1 and lv.status=1
    order by isdefault desc
    ";
    $driver_list = AceDB::getCol($sql);
    return $driver_list?implode('/',$driver_list):'无';
}

/**
 * 获取配送倒计时
 * 根据线路ID和门店ID计算配送倒计时
 *
 * @param int $lcid 线路ID
 * @param int $storeId 门店ID（可选，用于门店特定的倒计时）
 * @return string 格式化的倒计时时间 HH:MM:SS
 */
function getDeliveryCountdown($lcid) {
    // 获取线路的配送时间设置
    $sql = "SELECT starttime FROM lgt_car WHERE lcid = {$lcid}";
    $starttime = AceDB::getOne($sql);

    if (!$starttime) {
        // 如果没有找到线路信息，返回默认倒计时
        return '00:00:00';
    }

    
    $deliveryTime = strtotime(date('Y-m-d') . ' ' . $starttime.":00");
    $currentTime = time();
    $remainingSeconds = $deliveryTime - $currentTime;

    // 如果已经过了配送时间，返回00:00:00
    if ($remainingSeconds <= 0) {
        return '00:00:00';
    }

    // 格式化为 HH:MM:SS
    $hours = floor($remainingSeconds / 3600);
    $minutes = floor(($remainingSeconds % 3600) / 60);
    $seconds = $remainingSeconds % 60;

    return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
}

/**
 * 统一的JSON响应格式
 */
function jsonResponse($success, $message, $data = null) {
    $response = [
        'success' => $success,
        'message' => $message
    ];
    
    if ($data !== null) {
        $response['data'] = $data;
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}
?> 