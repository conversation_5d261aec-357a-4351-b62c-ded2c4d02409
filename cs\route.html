<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">
    <title>线路投屏</title>
    <link rel="stylesheet" href="style.css">
    <script src="../logistics/inc/js/jquery-2.1.3.min.js"></script>
    <script src="../logistics/inc/js/qrcode.min.js"></script>
    <script src="screen-adapter.js"></script>
</head>
<body>
    <div class="screen-container route-layout">
        <div class="back-btn-area">
            <button class="back-btn" onclick="history.back()">返回首页</button>
        </div>
        
        <div class="screen-header">
            <div class="screen-title">线路投屏系统</div>
            <div class="screen-time" id="current-time"></div>
            <button id="fullscreen-btn" class="fullscreen-btn">投屏模式</button>
        </div>
        
        <div class="route-main-content">
            <!-- 左侧：客户滚动 + 二维码区域 -->
            <div class="route-left-section">
                <div class="tech-lines"></div>
                <!-- 上部分：线路包含的客户滚动 -->
                <div class="route-stores-container">
                    <div class="route-stores-label">线路包含的门店</div>
                    <div class="route-stores" id="route-stores">
                        <div class="route-stores-scroll" id="route-stores-scroll">
                            <!-- 滚动展示的门店列表 -->
                        </div>
                    </div>
                </div>

                <!-- 下部分：二维码 -->
                <div class="route-qr-section">
                    <div class="qr-code" id="qr-code"></div>
                </div>
            </div>
            
            <!-- 右侧分块 -->
            <div class="route-right-section">
                <!-- 上部分：数据显示区域 -->
                <div class="route-counts-section">
                    <div class="route-count-card sorted-card">
                        <div class="tech-lines"></div>
                        <div class="count-label">已分拣单品数</div>
                        <div class="count-display-area">
                            <div class="count-value" id="sorted-count">0</div>
                        </div>
                    </div>
                    <div class="route-count-card unsorted-card">
                        <div class="tech-lines"></div>
                        <div class="count-label">未分拣单品数</div>
                        <div class="count-display-area">
                            <div class="count-value" id="unsorted-count">0</div>
                        </div>
                    </div>
                </div>
                
                <!-- 中部分：总任务完成进度 -->
                <div class="route-progress-section">
                    <div class="progress-label">总任务进度：</div>
                    <div class="progress-container">
                        <div class="progress-bar">
                            <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
                            <span class="progress-text" id="progress-text">0.00%</span>
                        </div>
                    </div>
                </div>
                
                <!-- 底部：信息条（三行） -->
                <div class="route-bottom-info">
                    <div class="route-info-row">
                        <div class="route-info-label">配送倒计时：</div>
                        <div class="route-info-value" id="countdown-timer">00:30:04</div>
                    </div>
                    <div class="route-info-row">
                        <div class="route-info-label">配送线路：</div>
                        <div class="route-info-value" id="route-name">加载中...</div>
                    </div>
                    <div class="route-info-row">
                        <div class="route-info-label">配送司机：</div>
                        <div class="route-info-value" id="delivery-driver">加载中...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="route.js"></script>
    <script>
    // 全屏切换逻辑
    (function() {
        var btn = document.getElementById('fullscreen-btn');
        function isFullScreen() {
            return document.fullscreenElement || document.webkitFullscreenElement || document.mozFullScreenElement || document.msFullscreenElement;
        }
        function setBtnText() {
            btn.textContent = isFullScreen() ? '退出全屏' : '投屏模式';
        }
        btn.addEventListener('click', function() {
            if (!isFullScreen()) {
                var docElm = document.documentElement;
                if (docElm.requestFullscreen) {
                    docElm.requestFullscreen();
                } else if (docElm.mozRequestFullScreen) {
                    docElm.mozRequestFullScreen();
                } else if (docElm.webkitRequestFullScreen) {
                    docElm.webkitRequestFullScreen();
                } else if (docElm.msRequestFullscreen) {
                    docElm.msRequestFullscreen();
                }
            } else {
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                } else if (document.mozCancelFullScreen) {
                    document.mozCancelFullScreen();
                } else if (document.webkitExitFullscreen) {
                    document.webkitExitFullscreen();
                } else if (document.msExitFullscreen) {
                    document.msExitFullscreen();
                }
            }
        });
        document.addEventListener('fullscreenchange', setBtnText);
        document.addEventListener('webkitfullscreenchange', setBtnText);
        document.addEventListener('mozfullscreenchange', setBtnText);
        document.addEventListener('MSFullscreenChange', setBtnText);
        setBtnText();
    })();
    </script>
</body>
</html> 