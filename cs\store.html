<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">
    <title>门店投屏</title>
    <link rel="stylesheet" href="style.css">
    <script src="../logistics/inc/js/jquery-2.1.3.min.js"></script>
    <script src="../logistics/inc/js/qrcode.min.js"></script>
    <script src="screen-adapter.js"></script>
</head>
<body>
    <div class="screen-container store-layout">
        <div class="back-btn-area">
            <button class="back-btn" onclick="history.back()">返回首页</button>
        </div>
        
        <div class="screen-header">
            <div class="screen-title">门店投屏系统</div>
            <div class="screen-time" id="current-time"></div>
            <button id="fullscreen-btn" class="fullscreen-btn">投屏模式</button>
        </div>
        
        <div class="store-main-content">
            <!-- 左侧大块 -->
            <div class="left-main-section">
                <div class="tech-lines"></div>
                <div class="store-name-section">
                    <div class="store-name-value" id="store-name">加载中...</div>
                </div>
                <div class="qr-section">
                    <div class="qr-code" id="qr-code"></div>
                </div>
            </div>
            
            <!-- 右侧分块 -->
            <div class="right-main-section">
                <!-- 上部分：已分拣和未分拣 -->
                <div class="counts-section">
                    <div class="count-card sorted-card">
                        <div class="tech-lines"></div>
                        <div class="count-label">已分拣单品数</div>
                        <div class="count-display-area">
                            <div class="count-value" id="sorted-count">0</div>
                        </div>
                    </div>
                    <div class="count-card unsorted-card">
                        <div class="tech-lines"></div>
                        <div class="count-label">未分拣单品数</div>
                        <div class="count-display-area">
                            <div class="count-value" id="unsorted-count">0</div>
                        </div>
                    </div>
                </div>
                
                <!-- 中部分：总任务完成进度 -->
                <div class="progress-section">
                    <div class="progress-label">总任务完成进度：</div>
                    <div class="progress-container">
                        <div class="progress-bar">
                            <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
                            <span class="progress-text" id="progress-text">0.00%</span>
                        </div>
                    </div>
                </div>
                
                <!-- 底部：信息条（三行） -->
                <div class="store-bottom-info">
                    <div class="store-info-row">
                        <div class="store-info-label">配送倒计时：</div>
                        <div class="store-info-value" id="countdown-timer">02:00:00</div>
                    </div>
                    <div class="store-info-row">
                        <div class="store-info-label">配送线路：</div>
                        <div class="store-info-value" id="delivery-route">加载中...</div>
                    </div>
                    <div class="store-info-row">
                        <div class="store-info-label">配送司机：</div>
                        <div class="store-info-value" id="delivery-driver">加载中...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="store.js"></script>
    <script>
    // 全屏切换逻辑
    (function() {
        var btn = document.getElementById('fullscreen-btn');
        function isFullScreen() {
            return document.fullscreenElement || document.webkitFullscreenElement || document.mozFullScreenElement || document.msFullscreenElement;
        }
        function setBtnText() {
            btn.textContent = isFullScreen() ? '退出全屏' : '投屏模式';
        }
        btn.addEventListener('click', function() {
            if (!isFullScreen()) {
                var docElm = document.documentElement;
                if (docElm.requestFullscreen) {
                    docElm.requestFullscreen();
                } else if (docElm.mozRequestFullScreen) {
                    docElm.mozRequestFullScreen();
                } else if (docElm.webkitRequestFullScreen) {
                    docElm.webkitRequestFullScreen();
                } else if (docElm.msRequestFullscreen) {
                    docElm.msRequestFullscreen();
                }
            } else {
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                } else if (document.mozCancelFullScreen) {
                    document.mozCancelFullScreen();
                } else if (document.webkitExitFullscreen) {
                    document.webkitExitFullscreen();
                } else if (document.msExitFullscreen) {
                    document.msExitFullscreen();
                }
            }
        });
        document.addEventListener('fullscreenchange', setBtnText);
        document.addEventListener('webkitfullscreenchange', setBtnText);
        document.addEventListener('mozfullscreenchange', setBtnText);
        document.addEventListener('MSFullscreenChange', setBtnText);
        setBtnText();
    })();
    </script>
</body>
</html> 